import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { v4 as uuidv4 } from "uuid";
import {
  StructuredAgentState,
  StructuredAgentStep,
  StepStatus,
  SubRequirement,
  ProjectKnowledge,
  CodeAnalysisResult,
  ExecutionPlan,
  UserFeedback,
} from "../../types/structuredAgent";

const initialState: StructuredAgentState = {
  currentStep: "requirement-input",
  stepStatuses: {
    "requirement-input": "pending",
    "requirement-decomposition": "pending",
    "user-confirmation-1": "pending",
    "project-analysis": "pending",
    "user-confirmation-2": "pending",
    "code-analysis": "pending",
    "user-confirmation-3": "pending",
    "plan-generation": "pending",
    "user-confirmation-4": "pending",
    "execution": "pending",
    "summary": "pending",
  },
  originalRequirement: "",
  subRequirements: [],
  projectKnowledge: [],
  codeAnalysis: [],
  executionPlans: [],
  userFeedbacks: [],
  isProcessing: false,
  currentProcessingMessage: "",
  executionResults: [],
  summary: null,
};

const structuredAgentSlice = createSlice({
  name: "structuredAgent",
  initialState,
  reducers: {
    // 重置流程
    resetFlow: (state) => {
      return { ...initialState };
    },

    // 设置当前步骤
    setCurrentStep: (state, action: PayloadAction<StructuredAgentStep>) => {
      state.currentStep = action.payload;
    },

    // 更新步骤状态
    updateStepStatus: (
      state,
      action: PayloadAction<{ step: StructuredAgentStep; status: StepStatus }>
    ) => {
      state.stepStatuses[action.payload.step] = action.payload.status;
    },

    // 设置原始需求
    setOriginalRequirement: (state, action: PayloadAction<string>) => {
      state.originalRequirement = action.payload;
      state.stepStatuses["requirement-input"] = "completed";
    },

    // 设置子需求
    setSubRequirements: (state, action: PayloadAction<SubRequirement[]>) => {
      state.subRequirements = action.payload;
      state.stepStatuses["requirement-decomposition"] = "completed";
      state.stepStatuses["user-confirmation-1"] = "waiting-for-user";
      state.currentStep = "user-confirmation-1";
    },

    // 添加用户反馈
    addUserFeedback: (state, action: PayloadAction<UserFeedback>) => {
      state.userFeedbacks.push(action.payload);

      // 根据反馈更新相应的确认步骤状态
      const stepId = action.payload.stepId;
      if (stepId === "requirement-decomposition") {
        state.stepStatuses["user-confirmation-1"] = "completed";
        if (action.payload.approved) {
          state.stepStatuses["project-analysis"] = "pending";
          state.currentStep = "project-analysis";
        }
      } else if (stepId === "project-analysis") {
        state.stepStatuses["user-confirmation-2"] = "completed";
        if (action.payload.approved) {
          state.stepStatuses["code-analysis"] = "pending";
          state.currentStep = "code-analysis";
        }
      } else if (stepId === "code-analysis") {
        state.stepStatuses["user-confirmation-3"] = "completed";
        if (action.payload.approved) {
          state.stepStatuses["plan-generation"] = "pending";
          state.currentStep = "plan-generation";
        }
      } else if (stepId === "plan-generation") {
        state.stepStatuses["user-confirmation-4"] = "completed";
        if (action.payload.approved) {
          state.stepStatuses["execution"] = "pending";
          state.currentStep = "execution";
        }
      }
    },

    // 设置项目知识
    setProjectKnowledge: (state, action: PayloadAction<ProjectKnowledge[]>) => {
      state.projectKnowledge = action.payload;
      state.stepStatuses["project-analysis"] = "completed";
      state.stepStatuses["user-confirmation-2"] = "waiting-for-user";
      state.currentStep = "user-confirmation-2";
    },

    // 设置代码分析结果
    setCodeAnalysis: (state, action: PayloadAction<CodeAnalysisResult[]>) => {
      state.codeAnalysis = action.payload;
      state.stepStatuses["code-analysis"] = "completed";
      state.stepStatuses["user-confirmation-3"] = "waiting-for-user";
      state.currentStep = "user-confirmation-3";
    },

    // 设置执行计划
    setExecutionPlans: (state, action: PayloadAction<ExecutionPlan[]>) => {
      state.executionPlans = action.payload;
      state.stepStatuses["plan-generation"] = "completed";
      state.stepStatuses["user-confirmation-4"] = "waiting-for-user";
      state.currentStep = "user-confirmation-4";
    },

    // 设置处理状态
    setProcessing: (
      state,
      action: PayloadAction<{ isProcessing: boolean; message?: string }>
    ) => {
      state.isProcessing = action.payload.isProcessing;
      state.currentProcessingMessage = action.payload.message || "";
    },

    // 添加执行结果
    addExecutionResult: (
      state,
      action: PayloadAction<{
        stepId: string;
        success: boolean;
        message: string;
      }>
    ) => {
      state.executionResults.push({
        ...action.payload,
        timestamp: Date.now(),
      });
    },

    // 设置最终总结
    setSummary: (
      state,
      action: PayloadAction<StructuredAgentState["summary"]>
    ) => {
      state.summary = action.payload;
      state.stepStatuses["summary"] = "completed";
    },

    // 进入下一步
    proceedToNextStep: (state) => {
      const steps: StructuredAgentStep[] = [
        "requirement-input",
        "requirement-decomposition",
        "user-confirmation-1",
        "project-analysis",
        "user-confirmation-2",
        "code-analysis",
        "user-confirmation-3",
        "plan-generation",
        "user-confirmation-4",
        "execution",
        "summary",
      ];
      
      const currentIndex = steps.indexOf(state.currentStep);
      if (currentIndex < steps.length - 1) {
        const nextStep = steps[currentIndex + 1];
        state.currentStep = nextStep;
        state.stepStatuses[nextStep] = "in-progress";
      }
    },
  },
});

export const {
  resetFlow,
  setCurrentStep,
  updateStepStatus,
  setOriginalRequirement,
  setSubRequirements,
  addUserFeedback,
  setProjectKnowledge,
  setCodeAnalysis,
  setExecutionPlans,
  setProcessing,
  addExecutionResult,
  setSummary,
  proceedToNextStep,
} = structuredAgentSlice.actions;

export default structuredAgentSlice.reducer;
