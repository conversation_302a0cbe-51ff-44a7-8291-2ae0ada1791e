import React, { useState } from "react";
import { useAppDispatch } from "../../../redux/hooks";
import { setOriginalRequirement, proceedToNextStep } from "../../../redux/slices/structuredAgentSlice";

export function RequirementInput() {
  const [requirement, setRequirement] = useState("");
  const dispatch = useAppDispatch();

  const handleSubmit = () => {
    if (requirement.trim()) {
      dispatch(setOriginalRequirement(requirement.trim()));
      dispatch(proceedToNextStep());
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          步骤 1: 输入您的需求
        </h2>
        
        <p className="text-gray-600 mb-6">
          请详细描述您希望实现的功能或需要解决的问题。越详细的描述将帮助我们更好地理解和分解您的需求。
        </p>

        <div className="space-y-4">
          <div>
            <label htmlFor="requirement" className="block text-sm font-medium text-gray-700 mb-2">
              需求描述 *
            </label>
            <textarea
              id="requirement"
              value={requirement}
              onChange={(e) => setRequirement(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="例如：我希望在现有的聊天应用中添加一个文件上传功能，用户可以上传图片和文档，并在聊天中预览..."
              className="w-full h-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              required
            />
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h4 className="text-sm font-medium text-blue-800 mb-2">💡 提示</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 描述具体的功能需求和预期效果</li>
              <li>• 提及相关的技术栈或约束条件</li>
              <li>• 说明用户场景和使用流程</li>
              <li>• 如有性能或安全要求，请一并说明</li>
            </ul>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={handleSubmit}
              disabled={!requirement.trim()}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              开始分析需求
            </button>
          </div>

          <div className="text-xs text-gray-500 text-center">
            按 Cmd/Ctrl + Enter 快速提交
          </div>
        </div>
      </div>
    </div>
  );
}
