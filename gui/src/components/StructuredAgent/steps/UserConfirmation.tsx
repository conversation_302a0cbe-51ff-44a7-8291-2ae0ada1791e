import React, { useState } from "react";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { addUserFeedback, proceedToNextStep } from "../../../redux/slices/structuredAgentSlice";

interface UserConfirmationProps {
  stepId: string;
}

const stepTitles: Record<string, string> = {
  "requirement-decomposition": "确认需求分解",
  "project-analysis": "确认项目分析",
  "code-analysis": "确认代码分析",
  "plan-generation": "确认执行计划"
};

const stepDescriptions: Record<string, string> = {
  "requirement-decomposition": "请确认上述需求分解是否准确和完整。如有需要调整的地方，请在建议中说明。",
  "project-analysis": "请确认项目分析结果是否准确。如有遗漏或错误的地方，请在建议中说明。",
  "code-analysis": "请确认代码分析结果是否准确。如有需要补充或修正的地方，请在建议中说明。",
  "plan-generation": "请确认执行计划是否可行。如有需要调整的步骤或顺序，请在建议中说明。"
};

export function UserConfirmation({ stepId }: UserConfirmationProps) {
  const dispatch = useAppDispatch();
  const [approved, setApproved] = useState<boolean | null>(null);
  const [suggestions, setSuggestions] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (approved === null) return;

    setIsSubmitting(true);
    
    try {
      // 添加用户反馈
      dispatch(addUserFeedback({
        stepId,
        approved,
        suggestions: suggestions.trim(),
        timestamp: Date.now()
      }));

      // 如果用户批准，继续下一步
      if (approved) {
        dispatch(proceedToNextStep());
      } else {
        // 如果用户不批准，需要重新处理上一步
        // 这里可以添加重新处理的逻辑
        console.log("用户不批准，需要重新处理:", suggestions);
      }
    } catch (error) {
      console.error("提交反馈失败:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const title = stepTitles[stepId] || "用户确认";
  const description = stepDescriptions[stepId] || "请确认上述结果是否符合您的预期。";

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          {title}
        </h2>
        
        <p className="text-gray-600 mb-6">
          {description}
        </p>

        <div className="space-y-6">
          {/* 确认选项 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              您的确认 *
            </label>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="approval"
                  value="approved"
                  checked={approved === true}
                  onChange={() => setApproved(true)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">
                  ✅ 确认无误，继续下一步
                </span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="radio"
                  name="approval"
                  value="rejected"
                  checked={approved === false}
                  onChange={() => setApproved(false)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">
                  ❌ 需要调整，请查看我的建议
                </span>
              </label>
            </div>
          </div>

          {/* 建议输入 */}
          <div>
            <label htmlFor="suggestions" className="block text-sm font-medium text-gray-700 mb-2">
              建议和意见 {approved === false && <span className="text-red-500">*</span>}
            </label>
            <textarea
              id="suggestions"
              value={suggestions}
              onChange={(e) => setSuggestions(e.target.value)}
              placeholder={
                approved === false 
                  ? "请详细说明需要调整的地方..." 
                  : "如有其他建议或意见，请在此说明（可选）"
              }
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              required={approved === false}
            />
          </div>

          {/* 提示信息 */}
          {approved === false && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <h4 className="text-sm font-medium text-yellow-800 mb-2">⚠️ 需要您的建议</h4>
              <p className="text-sm text-yellow-700">
                请详细说明需要调整的地方，这将帮助我们更好地满足您的需求。
              </p>
            </div>
          )}

          {approved === true && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <h4 className="text-sm font-medium text-green-800 mb-2">✅ 确认继续</h4>
              <p className="text-sm text-green-700">
                您已确认当前结果，我们将继续进行下一步。
              </p>
            </div>
          )}

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleSubmit}
              disabled={approved === null || (approved === false && !suggestions.trim()) || isSubmitting}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? "提交中..." : "提交确认"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
