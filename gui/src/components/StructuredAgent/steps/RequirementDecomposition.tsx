import React, { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { setSubRequirements, setProcessing } from "../../../redux/slices/structuredAgentSlice";
import { SubRequirement } from "../../../types/structuredAgent";
import { v4 as uuidv4 } from "uuid";

export function RequirementDecomposition() {
  const dispatch = useAppDispatch();
  const { originalRequirement, subRequirements, isProcessing, currentProcessingMessage } = useAppSelector(
    (state) => state.structuredAgent
  );
  const [hasStartedDecomposition, setHasStartedDecomposition] = useState(false);

  useEffect(() => {
    if (!hasStartedDecomposition && originalRequirement) {
      setHasStartedDecomposition(true);
      startDecomposition();
    }
  }, [originalRequirement, hasStartedDecomposition]);

  const startDecomposition = async () => {
    dispatch(setProcessing({ isProcessing: true, message: "正在分析和分解需求..." }));

    try {
      // 模拟AI分解需求的过程
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 这里应该调用AI服务来分解需求，现在先用模拟数据
      const mockSubRequirements: SubRequirement[] = [
        {
          id: uuidv4(),
          title: "用户界面设计",
          description: "设计文件上传的用户界面，包括拖拽区域、进度条和预览功能",
          priority: "high",
          estimatedComplexity: "moderate"
        },
        {
          id: uuidv4(),
          title: "文件上传API",
          description: "实现后端文件上传接口，支持多种文件格式验证和存储",
          priority: "high",
          estimatedComplexity: "complex"
        },
        {
          id: uuidv4(),
          title: "文件预览功能",
          description: "实现图片和文档的在线预览功能",
          priority: "medium",
          estimatedComplexity: "moderate"
        },
        {
          id: uuidv4(),
          title: "安全性验证",
          description: "添加文件类型检查、大小限制和恶意文件扫描",
          priority: "high",
          estimatedComplexity: "complex"
        }
      ];

      dispatch(setSubRequirements(mockSubRequirements));
    } catch (error) {
      console.error("需求分解失败:", error);
    } finally {
      dispatch(setProcessing({ isProcessing: false }));
    }
  };

  const getPriorityColor = (priority: SubRequirement["priority"]) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 border-red-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "low":
        return "bg-green-100 text-green-800 border-green-200";
    }
  };

  const getComplexityColor = (complexity: SubRequirement["estimatedComplexity"]) => {
    switch (complexity) {
      case "simple":
        return "bg-green-100 text-green-800 border-green-200";
      case "moderate":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "complex":
        return "bg-red-100 text-red-800 border-red-200";
    }
  };

  if (isProcessing) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            步骤 2: 需求分解
          </h2>
          
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">{currentProcessingMessage}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          步骤 2: 需求分解结果
        </h2>
        
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-800 mb-2">原始需求</h3>
          <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
            <p className="text-gray-700">{originalRequirement}</p>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-800">分解后的子需求</h3>
          
          {subRequirements.map((subReq, index) => (
            <div key={subReq.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <span className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-sm font-semibold">
                    {index + 1}
                  </span>
                  <h4 className="text-lg font-medium text-gray-900">{subReq.title}</h4>
                </div>
                
                <div className="flex space-x-2">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(subReq.priority)}`}>
                    {subReq.priority === "high" ? "高优先级" : subReq.priority === "medium" ? "中优先级" : "低优先级"}
                  </span>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getComplexityColor(subReq.estimatedComplexity)}`}>
                    {subReq.estimatedComplexity === "simple" ? "简单" : subReq.estimatedComplexity === "moderate" ? "中等" : "复杂"}
                  </span>
                </div>
              </div>
              
              <p className="text-gray-600 ml-11">{subReq.description}</p>
            </div>
          ))}
        </div>

        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <h4 className="text-sm font-medium text-blue-800 mb-2">📋 分解说明</h4>
          <p className="text-sm text-blue-700">
            我已经将您的需求分解为 {subRequirements.length} 个子需求。每个子需求都标注了优先级和复杂度，
            这将帮助我们更好地规划开发顺序和资源分配。请在下一步确认这些分解是否符合您的预期。
          </p>
        </div>
      </div>
    </div>
  );
}
