import React, { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { setSummary, resetFlow } from "../../../redux/slices/structuredAgentSlice";

export function Summary() {
  const dispatch = useAppDispatch();
  const { 
    originalRequirement, 
    subRequirements, 
    executionResults, 
    executionPlans,
    summary 
  } = useAppSelector((state) => state.structuredAgent);

  useEffect(() => {
    if (!summary) {
      generateSummary();
    }
  }, [summary]);

  const generateSummary = () => {
    const completedSteps = executionResults.filter(r => r.success);
    const failedSteps = executionResults.filter(r => !r.success);
    
    // 统计修改和创建的文件
    const modifiedFiles: string[] = [];
    const createdFiles: string[] = [];
    
    executionPlans.forEach(plan => {
      plan.steps.forEach(step => {
        const result = executionResults.find(r => r.stepId === step.id);
        if (result?.success && step.filePath) {
          if (step.type === "create") {
            createdFiles.push(step.filePath);
          } else if (step.type === "modify") {
            modifiedFiles.push(step.filePath);
          }
        }
      });
    });

    const summaryData = {
      completedRequirements: subRequirements
        .filter(req => {
          const planSteps = executionPlans
            .find(plan => plan.subRequirementId === req.id)?.steps || [];
          return planSteps.every(step => 
            executionResults.find(r => r.stepId === step.id)?.success
          );
        })
        .map(req => req.title),
      modifiedFiles: [...new Set(modifiedFiles)],
      createdFiles: [...new Set(createdFiles)],
      issues: failedSteps.map(step => step.message),
      recommendations: [
        "建议对新增功能进行全面测试",
        "检查代码风格和规范一致性",
        "更新相关文档和注释",
        "考虑添加错误处理和边界情况处理"
      ]
    };

    dispatch(setSummary(summaryData));
  };

  const handleStartNew = () => {
    dispatch(resetFlow());
  };

  if (!summary) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">正在生成总结...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const totalRequirements = subRequirements.length;
  const completedRequirements = summary.completedRequirements.length;
  const successRate = totalRequirements > 0 ? (completedRequirements / totalRequirements) * 100 : 0;

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          步骤 11: 任务完成总结
        </h2>
        
        {/* 总体完成情况 */}
        <div className="mb-6">
          <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">🎉 任务完成情况</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{completedRequirements}</div>
                <div className="text-sm text-gray-600">已完成需求</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{summary.createdFiles.length}</div>
                <div className="text-sm text-gray-600">新建文件</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{summary.modifiedFiles.length}</div>
                <div className="text-sm text-gray-600">修改文件</div>
              </div>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${successRate}%` }}
              />
            </div>
            <p className="text-center text-sm text-gray-600">
              完成率: {successRate.toFixed(1)}% ({completedRequirements}/{totalRequirements})
            </p>
          </div>
        </div>

        {/* 原始需求回顾 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-800 mb-3">📋 原始需求回顾</h3>
          <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
            <p className="text-gray-700">{originalRequirement}</p>
          </div>
        </div>

        {/* 已完成的需求 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-800 mb-3">✅ 已完成的需求</h3>
          {summary.completedRequirements.length > 0 ? (
            <div className="space-y-2">
              {summary.completedRequirements.map((req, index) => (
                <div key={index} className="flex items-center space-x-2 p-2 bg-green-50 border border-green-200 rounded-md">
                  <span className="text-green-600">✅</span>
                  <span className="text-gray-700">{req}</span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 italic">暂无完全完成的需求</p>
          )}
        </div>

        {/* 文件变更 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-800 mb-3">📁 文件变更</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 新建文件 */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">➕ 新建文件 ({summary.createdFiles.length})</h4>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {summary.createdFiles.map((file, index) => (
                  <div key={index} className="text-xs font-mono text-green-700 bg-green-50 px-2 py-1 rounded">
                    {file}
                  </div>
                ))}
              </div>
            </div>
            
            {/* 修改文件 */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">✏️ 修改文件 ({summary.modifiedFiles.length})</h4>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {summary.modifiedFiles.map((file, index) => (
                  <div key={index} className="text-xs font-mono text-blue-700 bg-blue-50 px-2 py-1 rounded">
                    {file}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 问题和建议 */}
        <div className="mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 遇到的问题 */}
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">⚠️ 遇到的问题</h3>
              {summary.issues.length > 0 ? (
                <div className="space-y-2">
                  {summary.issues.map((issue, index) => (
                    <div key={index} className="text-sm text-red-700 bg-red-50 px-3 py-2 rounded-md border border-red-200">
                      • {issue}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-green-600 text-sm">🎉 没有遇到问题，执行顺利！</p>
              )}
            </div>
            
            {/* 建议 */}
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">💡 后续建议</h3>
              <div className="space-y-2">
                {summary.recommendations.map((rec, index) => (
                  <div key={index} className="text-sm text-blue-700 bg-blue-50 px-3 py-2 rounded-md border border-blue-200">
                    • {rec}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-center space-x-4">
          <button
            onClick={handleStartNew}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            开始新任务
          </button>
        </div>
      </div>
    </div>
  );
}
