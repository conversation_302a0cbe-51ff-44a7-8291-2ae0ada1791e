import React, { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { setExecutionPlans, setProcessing } from "../../../redux/slices/structuredAgentSlice";
import { ExecutionPlan, ExecutionStep } from "../../../types/structuredAgent";
import { v4 as uuidv4 } from "uuid";

export function PlanGeneration() {
  const dispatch = useAppDispatch();
  const { subRequirements, codeAnalysis, executionPlans, isProcessing, currentProcessingMessage } = useAppSelector(
    (state) => state.structuredAgent
  );
  const [hasStartedPlanning, setHasStartedPlanning] = useState(false);

  useEffect(() => {
    if (!hasStartedPlanning && codeAnalysis.length > 0) {
      setHasStartedPlanning(true);
      startPlanGeneration();
    }
  }, [codeAnalysis, hasStartedPlanning]);

  const startPlanGeneration = async () => {
    dispatch(setProcessing({ isProcessing: true, message: "正在制定详细的执行计划..." }));

    try {
      // 模拟计划生成过程
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 这里应该调用AI服务来生成执行计划，现在先用模拟数据
      const mockExecutionPlans: ExecutionPlan[] = subRequirements.map((subReq, index) => {
        const steps: ExecutionStep[] = [
          {
            id: uuidv4(),
            type: "create",
            description: `创建${subReq.title}相关的组件文件`,
            filePath: `gui/src/components/${subReq.title.replace(/\s+/g, '')}/index.tsx`,
            order: 1,
            status: "pending"
          },
          {
            id: uuidv4(),
            type: "modify",
            description: `修改现有文件以集成${subReq.title}功能`,
            filePath: "gui/src/components/mainInput/TipTapEditor/TipTapEditor.tsx",
            order: 2,
            status: "pending"
          },
          {
            id: uuidv4(),
            type: "create",
            description: `添加${subReq.title}的后端API接口`,
            filePath: `core/api/${subReq.title.toLowerCase().replace(/\s+/g, '-')}.ts`,
            order: 3,
            status: "pending"
          },
          {
            id: uuidv4(),
            type: "test",
            description: `为${subReq.title}功能编写测试用例`,
            order: 4,
            status: "pending"
          }
        ];

        return {
          id: uuidv4(),
          subRequirementId: subReq.id,
          steps,
          estimatedTime: subReq.estimatedComplexity === "simple" ? "1-2小时" : 
                        subReq.estimatedComplexity === "moderate" ? "3-5小时" : "6-10小时",
          risks: [
            "可能需要调整现有代码结构",
            "新增依赖可能导致兼容性问题",
            "需要充分测试以确保功能稳定"
          ]
        };
      });

      dispatch(setExecutionPlans(mockExecutionPlans));
    } catch (error) {
      console.error("计划生成失败:", error);
    } finally {
      dispatch(setProcessing({ isProcessing: false }));
    }
  };

  const getStepTypeIcon = (type: ExecutionStep["type"]) => {
    switch (type) {
      case "create":
        return "➕";
      case "modify":
        return "✏️";
      case "delete":
        return "🗑️";
      case "test":
        return "🧪";
    }
  };

  const getStepTypeColor = (type: ExecutionStep["type"]) => {
    switch (type) {
      case "create":
        return "bg-green-100 text-green-800 border-green-200";
      case "modify":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "delete":
        return "bg-red-100 text-red-800 border-red-200";
      case "test":
        return "bg-purple-100 text-purple-800 border-purple-200";
    }
  };

  if (isProcessing) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            步骤 8: 制定执行计划
          </h2>
          
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">{currentProcessingMessage}</p>
              <p className="text-sm text-gray-500 mt-2">正在优化执行顺序...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          步骤 8: 执行计划
        </h2>
        
        <p className="text-gray-600 mb-6">
          基于代码分析结果，我已经为每个子需求制定了详细的执行计划，包括具体步骤、时间估算和风险评估：
        </p>

        <div className="space-y-6">
          {executionPlans.map((plan, planIndex) => {
            const subReq = subRequirements.find(req => req.id === plan.subRequirementId);
            
            return (
              <div key={plan.id} className="border border-gray-200 rounded-lg p-4">
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-medium text-gray-900">
                      计划 {planIndex + 1}: {subReq?.title}
                    </h3>
                    <div className="flex items-center space-x-2">
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        预计时间: {plan.estimatedTime}
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">{subReq?.description}</p>
                </div>

                {/* 执行步骤 */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">📋 执行步骤:</h4>
                  <div className="space-y-2">
                    {plan.steps.map((step, stepIndex) => (
                      <div key={step.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-md">
                        <span className="flex-shrink-0 w-6 h-6 bg-white border border-gray-300 rounded-full flex items-center justify-center text-xs font-semibold text-gray-600">
                          {step.order}
                        </span>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="text-lg">{getStepTypeIcon(step.type)}</span>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStepTypeColor(step.type)}`}>
                              {step.type === "create" ? "创建" : 
                               step.type === "modify" ? "修改" : 
                               step.type === "delete" ? "删除" : "测试"}
                            </span>
                          </div>
                          
                          <p className="text-sm text-gray-700 mb-1">{step.description}</p>
                          
                          {step.filePath && (
                            <p className="text-xs text-gray-500 font-mono">{step.filePath}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 风险评估 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">⚠️ 风险评估:</h4>
                  <ul className="space-y-1">
                    {plan.risks.map((risk, riskIndex) => (
                      <li key={riskIndex} className="text-sm text-orange-700 bg-orange-50 px-2 py-1 rounded">
                        • {risk}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <h4 className="text-sm font-medium text-blue-800 mb-2">📊 计划总结</h4>
          <div className="text-sm text-blue-700 space-y-1">
            <p>• 总共 {executionPlans.length} 个执行计划</p>
            <p>• 包含 {executionPlans.reduce((acc, plan) => acc + plan.steps.length, 0)} 个具体步骤</p>
            <p>• 预计总时间: {executionPlans.map(p => p.estimatedTime).join(", ")}</p>
            <p>• 执行顺序已按优先级和依赖关系优化</p>
          </div>
        </div>
      </div>
    </div>
  );
}
