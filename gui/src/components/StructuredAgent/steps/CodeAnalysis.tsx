import React, { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { setCodeAnalysis, setProcessing } from "../../../redux/slices/structuredAgentSlice";
import { CodeAnalysisResult } from "../../../types/structuredAgent";
import { v4 as uuidv4 } from "uuid";

export function CodeAnalysis() {
  const dispatch = useAppDispatch();
  const { subRequirements, projectKnowledge, codeAnalysis, isProcessing, currentProcessingMessage } = useAppSelector(
    (state) => state.structuredAgent
  );
  const [hasStartedAnalysis, setHasStartedAnalysis] = useState(false);

  useEffect(() => {
    if (!hasStartedAnalysis && subRequirements.length > 0 && projectKnowledge.length > 0) {
      setHasStartedAnalysis(true);
      startCodeAnalysis();
    }
  }, [subRequirements, projectKnowledge, hasStartedAnalysis]);

  const startCodeAnalysis = async () => {
    dispatch(setProcessing({ isProcessing: true, message: "正在分析相关代码和依赖关系..." }));

    try {
      // 模拟代码分析过程
      await new Promise(resolve => setTimeout(resolve, 4000));

      // 这里应该调用AI服务来分析代码，现在先用模拟数据
      const mockCodeAnalysis: CodeAnalysisResult[] = subRequirements.map((subReq) => ({
        id: uuidv4(),
        subRequirementId: subReq.id,
        affectedFiles: [
          "gui/src/components/mainInput/TipTapEditor/TipTapEditor.tsx",
          "gui/src/components/Chat/ChatInput.tsx",
          "core/protocol/core.ts"
        ],
        codeSnippets: [
          {
            filePath: "gui/src/components/mainInput/TipTapEditor/TipTapEditor.tsx",
            startLine: 45,
            endLine: 65,
            content: `// 文件上传相关代码需要在这里添加
const handleFileUpload = (files: FileList) => {
  // TODO: 实现文件上传逻辑
};`,
            reason: "需要在编辑器中添加文件上传功能"
          },
          {
            filePath: "core/protocol/core.ts",
            startLine: 120,
            endLine: 135,
            content: `// 需要添加文件上传相关的协议定义
export interface FileUploadRequest {
  file: File;
  metadata: FileMetadata;
}`,
            reason: "需要定义文件上传的协议接口"
          }
        ],
        dependencies: ["multer", "file-type", "sharp"],
        potentialIssues: [
          "需要考虑大文件上传的性能问题",
          "文件类型验证需要在前后端都实现",
          "需要处理上传进度显示"
        ]
      }));

      dispatch(setCodeAnalysis(mockCodeAnalysis));
    } catch (error) {
      console.error("代码分析失败:", error);
    } finally {
      dispatch(setProcessing({ isProcessing: false }));
    }
  };

  if (isProcessing) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            步骤 6: 代码分析
          </h2>
          
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">{currentProcessingMessage}</p>
              <p className="text-sm text-gray-500 mt-2">正在深度分析代码结构...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          步骤 6: 代码分析结果
        </h2>
        
        <p className="text-gray-600 mb-6">
          基于项目分析结果，我已经深入分析了每个子需求涉及的代码模块、文件和依赖关系：
        </p>

        <div className="space-y-6">
          {codeAnalysis.map((analysis, index) => {
            const subReq = subRequirements.find(req => req.id === analysis.subRequirementId);
            
            return (
              <div key={analysis.id} className="border border-gray-200 rounded-lg p-4">
                <div className="mb-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {index + 1}. {subReq?.title}
                  </h3>
                  <p className="text-sm text-gray-600">{subReq?.description}</p>
                </div>

                {/* 受影响的文件 */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">📁 受影响的文件:</h4>
                  <div className="flex flex-wrap gap-2">
                    {analysis.affectedFiles.map((file, fileIndex) => (
                      <span key={fileIndex} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md font-mono">
                        {file}
                      </span>
                    ))}
                  </div>
                </div>

                {/* 代码片段 */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">💻 关键代码片段:</h4>
                  <div className="space-y-3">
                    {analysis.codeSnippets.map((snippet, snippetIndex) => (
                      <div key={snippetIndex} className="border border-gray-200 rounded-md">
                        <div className="bg-gray-50 px-3 py-2 border-b border-gray-200">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-mono text-gray-700">{snippet.filePath}</span>
                            <span className="text-xs text-gray-500">行 {snippet.startLine}-{snippet.endLine}</span>
                          </div>
                          <p className="text-xs text-gray-600 mt-1">{snippet.reason}</p>
                        </div>
                        <pre className="p-3 text-sm bg-gray-900 text-gray-100 overflow-x-auto">
                          <code>{snippet.content}</code>
                        </pre>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 依赖关系 */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">📦 新增依赖:</h4>
                  <div className="flex flex-wrap gap-2">
                    {analysis.dependencies.map((dep, depIndex) => (
                      <span key={depIndex} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-md font-mono">
                        {dep}
                      </span>
                    ))}
                  </div>
                </div>

                {/* 潜在问题 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">⚠️ 潜在问题:</h4>
                  <ul className="space-y-1">
                    {analysis.potentialIssues.map((issue, issueIndex) => (
                      <li key={issueIndex} className="text-sm text-yellow-700 bg-yellow-50 px-2 py-1 rounded">
                        • {issue}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <h4 className="text-sm font-medium text-blue-800 mb-2">🔍 分析总结</h4>
          <p className="text-sm text-blue-700">
            代码分析已完成，共识别了 {codeAnalysis.reduce((acc, analysis) => acc + analysis.affectedFiles.length, 0)} 个需要修改的文件，
            {codeAnalysis.reduce((acc, analysis) => acc + analysis.dependencies.length, 0)} 个新增依赖，
            以及 {codeAnalysis.reduce((acc, analysis) => acc + analysis.potentialIssues.length, 0)} 个需要注意的潜在问题。
          </p>
        </div>
      </div>
    </div>
  );
}
