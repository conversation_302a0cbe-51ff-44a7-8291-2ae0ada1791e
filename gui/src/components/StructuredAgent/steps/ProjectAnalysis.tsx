import React, { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { setProjectKnowledge, setProcessing } from "../../../redux/slices/structuredAgentSlice";
import { ProjectKnowledge } from "../../../types/structuredAgent";
import { v4 as uuidv4 } from "uuid";

export function ProjectAnalysis() {
  const dispatch = useAppDispatch();
  const { subRequirements, projectKnowledge, isProcessing, currentProcessingMessage } = useAppSelector(
    (state) => state.structuredAgent
  );
  const [hasStartedAnalysis, setHasStartedAnalysis] = useState(false);

  useEffect(() => {
    if (!hasStartedAnalysis && subRequirements.length > 0) {
      setHasStartedAnalysis(true);
      startProjectAnalysis();
    }
  }, [subRequirements, hasStartedAnalysis]);

  const startProjectAnalysis = async () => {
    dispatch(setProcessing({ isProcessing: true, message: "正在分析项目结构和技术栈..." }));

    try {
      // 模拟项目分析过程
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 这里应该调用AI服务来分析项目，现在先用模拟数据
      const mockProjectKnowledge: ProjectKnowledge[] = [
        {
          id: uuidv4(),
          type: "structure",
          title: "前端架构",
          description: "基于React + TypeScript的现代前端架构，使用Redux进行状态管理",
          relevantFiles: ["gui/src/", "gui/src/components/", "gui/src/redux/"],
          importance: "critical"
        },
        {
          id: uuidv4(),
          type: "technology",
          title: "后端技术栈",
          description: "Node.js + TypeScript后端，使用Express框架",
          relevantFiles: ["core/", "binary/"],
          importance: "critical"
        },
        {
          id: uuidv4(),
          type: "pattern",
          title: "消息通信模式",
          description: "前后端通过消息协议进行通信，支持实时数据传输",
          relevantFiles: ["core/protocol/", "gui/src/context/IdeMessenger.ts"],
          importance: "important"
        },
        {
          id: uuidv4(),
          type: "dependency",
          title: "UI组件库",
          description: "使用Tailwind CSS和Heroicons进行UI设计",
          relevantFiles: ["gui/src/components/ui/"],
          importance: "helpful"
        }
      ];

      dispatch(setProjectKnowledge(mockProjectKnowledge));
    } catch (error) {
      console.error("项目分析失败:", error);
    } finally {
      dispatch(setProcessing({ isProcessing: false }));
    }
  };

  const getTypeIcon = (type: ProjectKnowledge["type"]) => {
    switch (type) {
      case "structure":
        return "🏗️";
      case "technology":
        return "⚙️";
      case "pattern":
        return "🔄";
      case "dependency":
        return "📦";
    }
  };

  const getImportanceColor = (importance: ProjectKnowledge["importance"]) => {
    switch (importance) {
      case "critical":
        return "bg-red-100 text-red-800 border-red-200";
      case "important":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "helpful":
        return "bg-green-100 text-green-800 border-green-200";
    }
  };

  if (isProcessing) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            步骤 4: 项目分析
          </h2>
          
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">{currentProcessingMessage}</p>
              <p className="text-sm text-gray-500 mt-2">这可能需要几分钟时间...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          步骤 4: 项目分析结果
        </h2>
        
        <p className="text-gray-600 mb-6">
          基于您的需求，我已经分析了项目的结构、技术栈和相关模式。以下是关键发现：
        </p>

        <div className="space-y-4">
          {projectKnowledge.map((knowledge, index) => (
            <div key={knowledge.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getTypeIcon(knowledge.type)}</span>
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">{knowledge.title}</h4>
                    <span className="text-sm text-gray-500 capitalize">{knowledge.type}</span>
                  </div>
                </div>
                
                <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getImportanceColor(knowledge.importance)}`}>
                  {knowledge.importance === "critical" ? "关键" : knowledge.importance === "important" ? "重要" : "有用"}
                </span>
              </div>
              
              <p className="text-gray-600 mb-3">{knowledge.description}</p>
              
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2">相关文件/目录:</h5>
                <div className="flex flex-wrap gap-2">
                  {knowledge.relevantFiles.map((file, fileIndex) => (
                    <span key={fileIndex} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md font-mono">
                      {file}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <h4 className="text-sm font-medium text-blue-800 mb-2">🔍 分析总结</h4>
          <p className="text-sm text-blue-700">
            我已经识别了 {projectKnowledge.length} 个关键的项目知识点，包括架构模式、技术栈和依赖关系。
            这些信息将帮助我们在下一步进行更精确的代码分析。
          </p>
        </div>
      </div>
    </div>
  );
}
