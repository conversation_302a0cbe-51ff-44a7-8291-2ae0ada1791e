import React, { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { addExecutionResult, proceedToNextStep, setProcessing } from "../../../redux/slices/structuredAgentSlice";

export function Execution() {
  const dispatch = useAppDispatch();
  const { executionPlans, executionResults, isProcessing, currentProcessingMessage } = useAppSelector(
    (state) => state.structuredAgent
  );
  const [hasStartedExecution, setHasStartedExecution] = useState(false);
  const [currentExecutingStep, setCurrentExecutingStep] = useState<string | null>(null);

  useEffect(() => {
    if (!hasStartedExecution && executionPlans.length > 0) {
      setHasStartedExecution(true);
      startExecution();
    }
  }, [executionPlans, hasStartedExecution]);

  const startExecution = async () => {
    dispatch(setProcessing({ isProcessing: true, message: "开始执行计划..." }));

    try {
      // 按顺序执行每个计划的每个步骤
      for (const plan of executionPlans) {
        for (const step of plan.steps) {
          setCurrentExecutingStep(step.id);
          dispatch(setProcessing({ 
            isProcessing: true, 
            message: `正在执行: ${step.description}` 
          }));

          // 模拟执行步骤
          await new Promise(resolve => setTimeout(resolve, 2000));

          // 模拟执行结果（大部分成功，偶尔失败）
          const success = Math.random() > 0.1; // 90% 成功率
          
          dispatch(addExecutionResult({
            stepId: step.id,
            success,
            message: success ? "执行成功" : "执行失败，需要手动处理"
          }));
        }
      }

      // 执行完成，进入总结步骤
      dispatch(proceedToNextStep());
    } catch (error) {
      console.error("执行失败:", error);
    } finally {
      setCurrentExecutingStep(null);
      dispatch(setProcessing({ isProcessing: false }));
    }
  };

  const getStepStatus = (stepId: string) => {
    const result = executionResults.find(r => r.stepId === stepId);
    if (result) {
      return result.success ? "completed" : "failed";
    }
    if (currentExecutingStep === stepId) {
      return "executing";
    }
    return "pending";
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return "✅";
      case "failed":
        return "❌";
      case "executing":
        return "⏳";
      case "pending":
        return "⏸️";
      default:
        return "⏸️";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600";
      case "failed":
        return "text-red-600";
      case "executing":
        return "text-blue-600";
      case "pending":
        return "text-gray-400";
      default:
        return "text-gray-400";
    }
  };

  const totalSteps = executionPlans.reduce((acc, plan) => acc + plan.steps.length, 0);
  const completedSteps = executionResults.filter(r => r.success).length;
  const failedSteps = executionResults.filter(r => !r.success).length;
  const progress = totalSteps > 0 ? ((completedSteps + failedSteps) / totalSteps) * 100 : 0;

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          步骤 10: 执行计划
        </h2>
        
        {/* 总体进度 */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-gray-800">执行进度</h3>
            <span className="text-sm text-gray-600">
              {completedSteps + failedSteps} / {totalSteps} 步骤
            </span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
            <div
              className="bg-blue-500 h-3 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-green-600">✅ 成功: {completedSteps}</span>
            <span className="text-red-600">❌ 失败: {failedSteps}</span>
            <span className="text-gray-500">⏸️ 待执行: {totalSteps - completedSteps - failedSteps}</span>
          </div>
        </div>

        {/* 当前执行状态 */}
        {isProcessing && (
          <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              <div>
                <h4 className="text-sm font-medium text-blue-800">正在执行</h4>
                <p className="text-sm text-blue-700">{currentProcessingMessage}</p>
              </div>
            </div>
          </div>
        )}

        {/* 执行详情 */}
        <div className="space-y-6">
          {executionPlans.map((plan, planIndex) => {
            const subReq = useAppSelector(state => 
              state.structuredAgent.subRequirements.find(req => req.id === plan.subRequirementId)
            );
            
            return (
              <div key={plan.id} className="border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  {planIndex + 1}. {subReq?.title}
                </h3>
                
                <div className="space-y-2">
                  {plan.steps.map((step, stepIndex) => {
                    const status = getStepStatus(step.id);
                    const result = executionResults.find(r => r.stepId === step.id);
                    
                    return (
                      <div key={step.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-md">
                        <span className="flex-shrink-0 w-6 h-6 bg-white border border-gray-300 rounded-full flex items-center justify-center text-xs font-semibold text-gray-600">
                          {step.order}
                        </span>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="text-lg">{getStatusIcon(status)}</span>
                            <span className={`text-sm font-medium ${getStatusColor(status)}`}>
                              {status === "completed" ? "已完成" : 
                               status === "failed" ? "执行失败" : 
                               status === "executing" ? "执行中" : "等待中"}
                            </span>
                          </div>
                          
                          <p className="text-sm text-gray-700 mb-1">{step.description}</p>
                          
                          {step.filePath && (
                            <p className="text-xs text-gray-500 font-mono mb-1">{step.filePath}</p>
                          )}
                          
                          {result && (
                            <p className={`text-xs ${result.success ? 'text-green-600' : 'text-red-600'}`}>
                              {result.message}
                            </p>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>

        {/* 执行完成提示 */}
        {!isProcessing && progress === 100 && (
          <div className="mt-6 bg-green-50 border border-green-200 rounded-md p-4">
            <h4 className="text-sm font-medium text-green-800 mb-2">🎉 执行完成</h4>
            <p className="text-sm text-green-700">
              所有计划步骤已执行完成！成功 {completedSteps} 步，失败 {failedSteps} 步。
              {failedSteps > 0 && "失败的步骤可能需要手动处理。"}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
