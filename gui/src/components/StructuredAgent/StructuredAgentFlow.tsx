import React from "react";
import { useAppSelector } from "../../redux/hooks";
import { StructuredAgentStep } from "../../types/structuredAgent";
import { RequirementInput } from "./steps/RequirementInput";
import { RequirementDecomposition } from "./steps/RequirementDecomposition";
import { UserConfirmation } from "./steps/UserConfirmation";
import { ProjectAnalysis } from "./steps/ProjectAnalysis";
import { CodeAnalysis } from "./steps/CodeAnalysis";
import { PlanGeneration } from "./steps/PlanGeneration";
import { Execution } from "./steps/Execution";
import { Summary } from "./steps/Summary";
import { StepProgress } from "./StepProgress";

export function StructuredAgentFlow() {
  const { currentStep, stepStatuses } = useAppSelector(
    (state) => state.structuredAgent
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case "requirement-input":
        return <RequirementInput />;
      case "requirement-decomposition":
        return <RequirementDecomposition />;
      case "user-confirmation-1":
        return <UserConfirmation stepId="requirement-decomposition" />;
      case "project-analysis":
        return <ProjectAnalysis />;
      case "user-confirmation-2":
        return <UserConfirmation stepId="project-analysis" />;
      case "code-analysis":
        return <CodeAnalysis />;
      case "user-confirmation-3":
        return <UserConfirmation stepId="code-analysis" />;
      case "plan-generation":
        return <PlanGeneration />;
      case "user-confirmation-4":
        return <UserConfirmation stepId="plan-generation" />;
      case "execution":
        return <Execution />;
      case "summary":
        return <Summary />;
      default:
        return <RequirementInput />;
    }
  };

  return (
    <div className="flex h-full flex-col">
      {/* 进度指示器 */}
      <div className="border-b border-gray-200 p-4">
        <StepProgress currentStep={currentStep} stepStatuses={stepStatuses} />
      </div>

      {/* 当前步骤内容 */}
      <div className="flex-1 overflow-auto p-4">
        {renderCurrentStep()}
      </div>
    </div>
  );
}
