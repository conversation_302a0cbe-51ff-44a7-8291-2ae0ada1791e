import React from "react";
import { CheckIcon, ClockIcon, ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import { StructuredAgentStep, StepStatus } from "../../types/structuredAgent";

interface StepProgressProps {
  currentStep: StructuredAgentStep;
  stepStatuses: Record<StructuredAgentStep, StepStatus>;
}

const stepLabels: Record<StructuredAgentStep, string> = {
  "requirement-input": "需求输入",
  "requirement-decomposition": "需求分解",
  "user-confirmation-1": "确认需求",
  "project-analysis": "项目分析",
  "user-confirmation-2": "确认分析",
  "code-analysis": "代码分析",
  "user-confirmation-3": "确认代码",
  "plan-generation": "制定计划",
  "user-confirmation-4": "确认计划",
  "execution": "执行计划",
  "summary": "完成总结",
};

const steps: StructuredAgentStep[] = [
  "requirement-input",
  "requirement-decomposition",
  "user-confirmation-1",
  "project-analysis",
  "user-confirmation-2",
  "code-analysis",
  "user-confirmation-3",
  "plan-generation",
  "user-confirmation-4",
  "execution",
  "summary",
];

function getStatusIcon(status: StepStatus, isActive: boolean) {
  switch (status) {
    case "completed":
      return <CheckIcon className="h-4 w-4 text-green-500" />;
    case "in-progress":
      return <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />;
    case "waiting-for-user":
      return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />;
    case "pending":
      return <ClockIcon className={`h-4 w-4 ${isActive ? "text-blue-500" : "text-gray-400"}`} />;
    default:
      return <ClockIcon className="h-4 w-4 text-gray-400" />;
  }
}

function getStepClassName(status: StepStatus, isActive: boolean) {
  const baseClasses = "flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors";
  
  if (status === "completed") {
    return `${baseClasses} bg-green-50 text-green-700 border border-green-200`;
  }
  
  if (status === "in-progress") {
    return `${baseClasses} bg-blue-50 text-blue-700 border border-blue-200`;
  }
  
  if (status === "waiting-for-user") {
    return `${baseClasses} bg-yellow-50 text-yellow-700 border border-yellow-200`;
  }
  
  if (isActive) {
    return `${baseClasses} bg-blue-50 text-blue-600 border border-blue-200`;
  }
  
  return `${baseClasses} bg-gray-50 text-gray-500 border border-gray-200`;
}

export function StepProgress({ currentStep, stepStatuses }: StepProgressProps) {
  const currentStepIndex = steps.indexOf(currentStep);

  return (
    <div className="w-full">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">结构化智能体流程</h2>
      
      {/* 步骤列表 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
        {steps.map((step, index) => {
          const status = stepStatuses[step];
          const isActive = step === currentStep;
          const stepNumber = index + 1;
          
          return (
            <div
              key={step}
              className={getStepClassName(status, isActive)}
            >
              <div className="flex items-center space-x-2">
                <span className="flex-shrink-0 w-6 h-6 rounded-full bg-gray-200 text-gray-600 text-xs font-bold flex items-center justify-center">
                  {stepNumber}
                </span>
                {getStatusIcon(status, isActive)}
                <span className="truncate">{stepLabels[step]}</span>
              </div>
            </div>
          );
        })}
      </div>
      
      {/* 进度条 */}
      <div className="mt-4">
        <div className="flex justify-between text-sm text-gray-600 mb-1">
          <span>进度</span>
          <span>{Math.round((currentStepIndex / (steps.length - 1)) * 100)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStepIndex / (steps.length - 1)) * 100}%` }}
          />
        </div>
      </div>
    </div>
  );
}
