// 结构化Agent流程的类型定义

export type StructuredAgentStep = 
  | "requirement-input"      // 1. 用户输入需求
  | "requirement-decomposition" // 2. 需求分解
  | "user-confirmation-1"    // 3. 用户确认需求分解
  | "project-analysis"       // 4. 项目了解
  | "user-confirmation-2"    // 5. 用户确认项目分析
  | "code-analysis"          // 6. 代码分析
  | "user-confirmation-3"    // 7. 用户确认代码分析
  | "plan-generation"        // 8. 制定计划
  | "user-confirmation-4"    // 9. 用户确认计划
  | "execution"              // 10. 执行计划
  | "summary";               // 11. 总结完成

export type StepStatus = "pending" | "in-progress" | "completed" | "waiting-for-user";

export interface SubRequirement {
  id: string;
  title: string;
  description: string;
  priority: "high" | "medium" | "low";
  estimatedComplexity: "simple" | "moderate" | "complex";
}

export interface ProjectKnowledge {
  id: string;
  type: "structure" | "technology" | "pattern" | "dependency";
  title: string;
  description: string;
  relevantFiles: string[];
  importance: "critical" | "important" | "helpful";
}

export interface CodeAnalysisResult {
  id: string;
  subRequirementId: string;
  affectedFiles: string[];
  codeSnippets: {
    filePath: string;
    startLine: number;
    endLine: number;
    content: string;
    reason: string;
  }[];
  dependencies: string[];
  potentialIssues: string[];
}

export interface ExecutionPlan {
  id: string;
  subRequirementId: string;
  steps: ExecutionStep[];
  estimatedTime: string;
  risks: string[];
}

export interface ExecutionStep {
  id: string;
  type: "create" | "modify" | "delete" | "test";
  description: string;
  filePath?: string;
  order: number;
  status: "pending" | "in-progress" | "completed" | "failed";
}

export interface UserFeedback {
  stepId: string;
  approved: boolean;
  suggestions: string;
  timestamp: number;
}

export interface StructuredAgentState {
  currentStep: StructuredAgentStep;
  stepStatuses: Record<StructuredAgentStep, StepStatus>;
  
  // 步骤数据
  originalRequirement: string;
  subRequirements: SubRequirement[];
  projectKnowledge: ProjectKnowledge[];
  codeAnalysis: CodeAnalysisResult[];
  executionPlans: ExecutionPlan[];
  
  // 用户反馈
  userFeedbacks: UserFeedback[];
  
  // 流程控制
  isProcessing: boolean;
  currentProcessingMessage: string;
  
  // 执行状态
  executionResults: {
    stepId: string;
    success: boolean;
    message: string;
    timestamp: number;
  }[];
  
  // 最终总结
  summary: {
    completedRequirements: string[];
    modifiedFiles: string[];
    createdFiles: string[];
    issues: string[];
    recommendations: string[];
  } | null;
}
